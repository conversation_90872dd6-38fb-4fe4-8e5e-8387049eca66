// ignore_for_file: unnecessary_null_comparison

import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class StrUtils {
  static String formatDateMMDD({String? input}) {
    if (input == null) {
      return '-';
    }
    if (input.contains('-')) {
      List<String> parts = input.split('-');
      return '${parts[1]}.${parts[2]}';
    } else if (input.contains('/')) {
      List<String> parts = input.split('/');
      return '${parts[0]}.${parts[1]}';
    } else {
      List<String> parts = input.split(' ');
      return '${parts[0]}.${parts[1]}';
    }
  }

  static String formatDateMMDDSS({String? input}) {
    try {
      if (input == null) {
        return '-';
      }
      List<String> parts = input.split('-');
      return '${parts[1]}.${parts[2]}';
    } catch (e) {
      return '-';
    }
  }

  static String formatNumber({String input = ''}) {
    if (input == '') {
      return '0';
    }
    // 使用千分位格式化（自动补全，逗号分隔符）
    final formatter = NumberFormat('#,###.##');
    final formattedNumber = formatter.format(double.tryParse(input) ?? 0);
    return formattedNumber;
  }

  static String formatDurationInDecimalHours(int seconds) {
    // 计算总小时（转换为浮点数）
    double totalHours = seconds / 3600;
    // 保留两位小数（四舍五入）
    String formattedHours = totalHours.toStringAsFixed(2);
    return "$formattedHours";
  }

  static String formatDDMM(String inputDate) {
    // 解析字符串为 DateTime 对象
    DateTime date = DateTime.parse(inputDate);
    // 使用 DateFormat 格式化日期为 "dd/MM"
    String formattedDate = DateFormat('dd/MM').format(date);
    return formattedDate;
  }

  static String formatYYMMDDHHSSMM(int timestamp) {
    // 转换为 DateTime（UTC 时间）
    DateTime date =
        DateTime.fromMillisecondsSinceEpoch(timestamp * 1000, isUtc: true);

    // 格式化日期
    DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    String formatted = formatter.format(date.toLocal()); // 转换为本地时区

    return formatted;
  }

  static String formatMoney(param) {
    try {
      // 确保参数是数字类型
      double value;
      if (param is String) {
        value = double.tryParse(param) ?? 0.0;
      } else if (param is num) {
        value = param.toDouble();
      } else {
        value = 0.0;
      }

      final formatter = NumberFormat("###,###.##");
      String formattedNumber = formatter.format(value);
      formattedNumber = formattedNumber.replaceAll(',', '*');
      formattedNumber = formattedNumber.replaceAll('.', ',');
      return formattedNumber.replaceAll('*', '.');
    } catch (e) {
      return '0,00';
    }
  }

  static String formatMoney7(param) {
    try {
      // 确保参数是数字类型
      double value;
      if (param is String) {
        value = double.tryParse(param) ?? 0.0;
      } else if (param is num) {
        value = param.toDouble();
      } else {
        value = 0.0;
      }

      final formatter = NumberFormat("###,###.#######");
      final formattedNumber = formatter.format(value);
      return formattedNumber;
    } catch (e) {
      return '0.0000000';
    }
  }

  static String jszb(String num1, String num2, String num3, String param) {
    // 定义数值
    final numbers = [
      double.parse('${num1}'),
      double.parse('${num2}'),
      double.parse('${num3}')
    ];

    // 计算总和
    final total = numbers.reduce((a, b) => a + b);

    // 计算每个数值的百分比
    // final percentages = numbers.map((num) {
    //   return (num / total * 100).toStringAsFixed(2) + '%';
    // }).toList();
    return (double.parse('${param}') / total * 100).toStringAsFixed(0);
  }

  static void toH5(url) async {
    if (!await launchUrl(
      Uri.parse(url),
      mode: LaunchMode.externalApplication,
    )) {
      throw Exception('Could not launch $url');
    }
  }

  static String formatNumberUnit(int number, {int decimalPlaces = 1}) {
    if (number == null) return '';
    if (number >= 10000) {
      final value = number / 10000;
      return '${value.toStringAsFixed(decimalPlaces)}w';
    } else if (number >= 1000) {
      final value = number / 1000;
      return '${value.toStringAsFixed(decimalPlaces)}k';
    }
    return number.toString();
  }

  static String getLang() {
    logger.d('当前语言${Get.locale?.languageCode}');
    // 印地语也使用英语
    if (Get.locale?.languageCode == 'en') {
      return 'en_us';
    } else if (Get.locale?.languageCode == 'zh') {
      return 'zh_CN';
    } else if (Get.locale?.languageCode == 'hi') {
      return 'hi_IN';
    } else {
      return 'id_ID';
    }
  }

  static String formatTime(int seconds) {
    int mins = seconds ~/ 60;
    int secs = seconds % 60;
    return '${mins.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  /// 货币单位
  static String currencyUnit() {
    String language = CustomSpUtil.getLanguage();
    if (language == 'en') {
      return 'USD';
    } else if (language == 'zh') {
      return 'CNY';
    } else if (language == 'hi') {
      return 'INR';
    } else {
      return 'IDR';
    }
  }

  /// 货币符号
  static String currencySymbol() {
    String language = CustomSpUtil.getLanguage();
    if (language == 'en') {
      return '\$';
    } else if (language == 'zh') {
      return '￥';
    } else if (language == 'hi') {
      return 'INR';
    } else {
      return 'Rp';
    }
  }
}
