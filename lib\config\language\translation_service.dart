import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/config/language/id_ID.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:get/get.dart';
import 'package:tencent_chat_i18n_tool/tools/i18n_tool.dart';
import 'en_US.dart';
import 'zh_CN.dart';

class TranslationService extends Translations {
  // static const fallbackLocale = Locale('id', 'ID');
  static const fallbackLocale = Locale('en', 'US');

  @override
  Map<String, Map<String, String>> get keys => {
        'zh_CN': zh_CN,
        'en_US': en_US,
        'id_ID': id_ID,
        'hi_IN': en_US, // 印地语使用英语
      };

  static Locale? get locale {
    var locale = CustomSpUtil.getLanguage();
    if (locale == "") {
      /// 设置当前语言
      I18nUtils(null, "hi"); // 默认使用印地语
      return const Locale('en', 'US'); // 印地语使用英语
    } else {
      if (locale == "zh") {
        I18nUtils(null, "zh-Hans");
        return const Locale('zh', 'CN');
      } else if (locale == 'en') {
        I18nUtils(null, "en");
        return const Locale('en', 'US');
      } else if (locale == 'id') {
        I18nUtils(null, "id");
        return const Locale('id', 'ID');
      } else if (locale == 'hi') {
        I18nUtils(null, "hi");
        // 印地语使用英语
        return const Locale('en', 'US');
      }
    }
  }
}
