import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:get/get.dart';
import 'package:screen_capture_event/screen_capture_event.dart';

class CaptureUtils {
  static ScreenCaptureEvent _screenCaptureEvent = ScreenCaptureEvent();

  static RxBool isRecording = false.obs;

  static void init() {
    // 录屏检测
    _screenCaptureEvent.addScreenRecordListener((recorded) {
      isRecording.value = recorded;
    });

    _screenCaptureEvent.watch();
  }

  /// 禁用录屏/截屏
  static disableScreenShot() {
    logger.d('禁用录屏/截屏');
    _screenCaptureEvent.preventAndroidScreenShot(true);
  }

  static dispose() {
    logger.d('停止录屏检测');
    isRecording.value = false;
    _screenCaptureEvent.preventAndroidScreenShot(false);
    _screenCaptureEvent.dispose();
  }
}
