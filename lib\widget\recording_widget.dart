import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';

import '../res/rc.dart';

/// 录屏提示
class RecordingWidget extends StatelessWidget {
  const RecordingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Center(
        child: Text(
          '请勿录屏',
          style: TextStyle(
              fontSize: 15.sp, fontWeight: FontWeight.w400, color: RC.white),
        ),
      ),
    );
  }
}
