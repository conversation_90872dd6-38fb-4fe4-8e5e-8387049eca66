import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:flutter_starry_sky_box/config/language/translation_service.dart';
import 'package:flutter_starry_sky_box/global.dart';
import 'package:flutter_starry_sky_box/pages/chat/provider/custom_sticker_package.dart';
import 'package:flutter_starry_sky_box/pages/chat/provider/local_setting.dart';
import 'package:flutter_starry_sky_box/routers/app_pages.dart';
import 'package:flutter_starry_sky_box/utils/crash_logger.dart';
import 'package:flutter_starry_sky_box/widget/default_page/not_found_page.dart';
import 'package:flutter_starry_sky_box/widget/scroll/no_shadow_scroll_behavior.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';

void main() async {
  await Global.init();
  runApp(
    MultiProvider(
      providers: [
        if (!kIsWeb) ChangeNotifierProvider(create: (_) => Global.chatTheme),
        if (!kIsWeb)
          ChangeNotifierProvider(create: (_) => CustomStickerPackageData()),
        ChangeNotifierProvider(create: (_) => LocalSetting()),
      ],
      child: const App(),
    ),
  );
}

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(Get.context!).requestFocus(FocusNode());
      },
      child: GetMaterialApp(
          navigatorKey: Get.key,
          title: 'FunShot',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            // colorScheme: ColorScheme.fromSeed(seedColor: Colors.red, background: Colors.blue, onBackground: Colors.yellow),
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            bottomNavigationBarTheme: const BottomNavigationBarThemeData(
              selectedIconTheme: IconThemeData(opacity: 1.0), // 选中图标的不透明度
              unselectedIconTheme: IconThemeData(opacity: 0.7), // 未选中图标的不透明度
            ),
            splashFactory: NoSplash.splashFactory,
            // 全局移除水波纹效果
            highlightColor: Colors.transparent,
            // 全局移除点击高亮效果
            appBarTheme: const AppBarTheme(
              surfaceTintColor: Colors.transparent,
            ),
          ),
          builder: EasyLoading.init(builder: (context, child) {
            return MediaQuery(
              // 设置文字大小不随系统设置改变
              data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
              child: ScrollConfiguration(
                behavior: NoShadowScrollBehavior(),
                // child: child ?? const Material(),
                child: Stack(
                  children: [
                    child ?? const Material(),
                    // Positioned(
                    //   right: -15,
                    //   top: -15,
                    //   child: CustomPaint(
                    //     size: const Size(64, 64), // 指定绘制区域的大小
                    //     painter: TrapezoidPainter(),
                    //   ),
                    // )
                  ],
                ),
              ),
            );
          }),
          // initialRoute: CustomSpUtil.getGuide() ? AppPages.initial : AppRoutes.loginOrRegister,
          initialRoute: AppPages.initial,
          unknownRoute: GetPage(name: '/404', page: () => const NotFoundPage()),
          localizationsDelegates: const [
            //是Flutter的一个本地化委托，用于提供Material组件库的本地化支持
            GlobalMaterialLocalizations.delegate,
            //用于提供通用部件（Widgets）的本地化支持
            GlobalWidgetsLocalizations.delegate,
            //用于提供Cupertino风格的组件的本地化支持
            GlobalCupertinoLocalizations.delegate,
            PickerLocalizationsDelegate.delegate,
          ],
          supportedLocales: const [
            Locale('zh', 'CN'),
            Locale('en', 'US'),
            Locale('id', 'ID'),
            Locale('hi', 'IN')
          ],
          translations: TranslationService(),
          locale: TranslationService.locale,
          // fallbackLocale: TranslationService.fallbackLocale,
          // localeResolutionCallback: (locale, supportedLocales) {
          //   // 检测和选择合适的语言
          //   for (var supportedLocale in supportedLocales) {
          //     if (supportedLocale.languageCode == locale?.languageCode && supportedLocale.countryCode == locale?.countryCode) {
          //       return supportedLocale;
          //     }
          //   }
          //   // 默认使用英语
          //   return supportedLocales.first;
          // },
          getPages: AppPages.routes,
          navigatorObservers:
              !PlatformUtils().isMobile ? [] : [TUICallKit.navigatorObserver]),
    );
  }
}
