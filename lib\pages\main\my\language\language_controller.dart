import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/pages/main/home/<USER>/short_video_controller.dart';
import 'package:flutter_starry_sky_box/pages/main/main_controller.dart';
import 'package:flutter_starry_sky_box/pages/main/my/my_center/my_center_controller.dart';
import 'package:flutter_starry_sky_box/routers/app_routes.dart';
import 'package:flutter_starry_sky_box/utils/custom_constant.dart';
import 'package:flutter_starry_sky_box/utils/custom_event_util.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:get/get.dart';
import 'package:tencent_chat_i18n_tool/tools/i18n_tool.dart';

import '../../../login/login_controller.dart';

class LanguageController extends GetxController {
  List<LanguageDTO> languageList = [
    // LanguageDTO(name: 'हिन्दी', desc: 'Hindi', icon: 'assets/images/png/language_01.png'),
    LanguageDTO(
        name: 'English',
        desc: 'English',
        icon: 'assets/images/png/language_02.png',
        locale: 'en'),
    // LanguageDTO(name: 'বাংলা', desc: 'Bengali', icon: 'assets/images/png/language_03.png'),
    // LanguageDTO(name: 'मराठी', desc: 'Marathi', icon: 'assets/images/png/language_04.png'),
    // LanguageDTO(name: 'తెలుగు', desc: 'Telugu', icon: 'assets/images/png/language_05.png'),
    // LanguageDTO(name: 'தமிழ்', desc: 'Tamil', icon: 'assets/images/png/language_06.png'),
    LanguageDTO(
        name: '简体中文',
        desc: 'Urdu',
        icon: 'assets/images/png/language_07.png',
        locale: 'zh'),
    LanguageDTO(
        name: 'BahasaIndonesia',
        desc: 'BahasaIndonesia',
        icon: 'assets/images/png/language_03.png',
        locale: 'id'),
    LanguageDTO(
      name: 'Hindi',
      desc: 'Hindi',
      icon: 'assets/images/png/language_04.png',
      locale: 'hi',
    ),
  ];

  @override
  void onInit() {
    super.onInit();

    String local = CustomSpUtil.getLanguage();
    int index = -1;
    for (int i = 0; i < languageList.length; i++) {
      if (local == languageList[i].locale) {
        index = i;
      }
    }
    if (index == -1) {
      for (int i = 0; i < languageList.length; i++) {
        if ('en' == languageList[i].locale) {
          languageList[i].isSelect = true;
          update();
        }
      }
    } else {
      languageList[index].isSelect = true;
    }
    update();
  }

  changeLanguage(index) {
    if (languageList[index].isSelect == true) {
      return;
    }

    languageList.forEach((element) {
      element.isSelect = false;
    });

    languageList[index].isSelect = true;

    if (index == 0) {
      I18nUtils(null, "en");
    } else if (index == 1) {
      I18nUtils(null, "zh-Hans");
    } else if (index == 2) {
      I18nUtils(null, "id");
    } else if (index == 3) {
      I18nUtils(null, "en"); // 印地语使用英语文本
    }
    update();
  }

  // 确认
  confirm() {
    var selectedLanguage =
        languageList.firstWhere((item) => item.isSelect == true);
    logger.d(selectedLanguage.locale);
    Get.updateLocale(Locale(selectedLanguage.locale));
    CustomSpUtil.setLanguage(selectedLanguage.locale);
    eventBus.fire(const BottomNavigationBarEvent(CustomConstant.LANG_REFRESH));
    _update();
    // CustomToast.showTextToast('${Get.locale?.languageCode == 'en'}');
  }

  /// 切换语言更新
  _update() {
    logger.d('切换语言更新内容');
    // 切换语言更新货币
    Get.find<MyCenterController>().contractGetUserInfo();

    if (Get.currentRoute == AppRoutes.main) {
      // 切换语言刷新短视频
      Get.find<ShortVideoController>().videoList.clear();
      Get.find<ShortVideoController>().isLoading = true;
      Get.find<ShortVideoController>().isLastData = false;
      Get.find<ShortVideoController>().update();
      Get.find<ShortVideoController>().getVideoList();
    }
  }
}

class LanguageDTO {
  final String name;
  final String desc;
  final String icon;
  bool isSelect;
  String locale;

  LanguageDTO(
      {this.name = '',
      this.desc = '',
      this.icon = '',
      this.isSelect = false,
      this.locale = 'en'});
}
