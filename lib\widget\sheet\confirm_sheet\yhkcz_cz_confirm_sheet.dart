import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/pages/main/my/recharge/recharge_controller.dart';
import 'package:flutter_starry_sky_box/pages/main/my/user_store.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/res/styles.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:flutter_starry_sky_box/utils/str_utils.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:get/get.dart';

class YHKCZConfirmSheet {
  static show(
    exchangeRate, {
    Function? onTap,
    Function? onConfirm,
  }) {
    showModalBottomSheet(
        context: Get.context!,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        isScrollControlled: true,
        builder: (context) {
          return Material(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(19.r),
                  topRight: Radius.circular(19.r)),
              child: GetBuilder<RechargeController>(
                  init: RechargeController(),
                  builder: (rechargeController) {
                    return ClipRRect(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(19.r),
                          topRight: Radius.circular(19.r)),
                      child: Container(
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/png/ti_confirm_bg.png'),
                                fit: BoxFit.cover)),
                        width: double.infinity,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  IconButton(
                                      onPressed: () {}, icon: const SizedBox()),
                                  SizedBox(
                                    child: Row(
                                      children: [
                                        SizedBox(
                                          width: 20.w,
                                          height: 20.w,
                                          child: Image.asset(
                                              'assets/images/png/confirm_tips.png'),
                                        ),
                                        SizedBox(
                                          width: 5.w,
                                        ),
                                        Text(
                                          'confirmation'.tr,
                                          style: Styles.font_custom(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w600,
                                              color: RC.black),
                                        )
                                      ],
                                    ),
                                  ),
                                  IconButton(
                                      onPressed: () {
                                        Get.back();
                                      },
                                      icon: SizedBox(
                                        width: 13.w,
                                        height: 13.w,
                                        child: Image.asset(
                                            'assets/images/png/confirm_close.png'),
                                      ))
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 23.w,
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 21.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 6.w),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'recharge_amount'.tr,
                                          style: Styles.font_custom(
                                              fontSize: 13.sp,
                                              fontWeight: FontWeight.w400,
                                              color: RC.color707B7B),
                                        ),
                                        RichText(
                                            text: TextSpan(children: [
                                          TextSpan(
                                              text:
                                                  '${double.parse('${rechargeController.moneyController.text}').toStringAsFixed(2)} ',
                                              style: Styles.font_custom(
                                                  fontSize: 13.sp,
                                                  fontWeight: FontWeight.w500,
                                                  color: RC.black)),
                                          TextSpan(
                                              text:
                                                  StrUtils.getLang() == 'id_ID'
                                                      ? 'IDR'.tr
                                                      : StrUtils.getLang() ==
                                                              'hi_IN'
                                                          ? 'INR'
                                                          : 'USD',
                                              // text: 'USDT'.tr,
                                              style: Styles.font_custom(
                                                  fontSize: 13.sp,
                                                  fontWeight: FontWeight.w500,
                                                  color: RC.color707B7B))
                                        ]))
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 6.w),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'gain'.tr,
                                          style: Styles.font_custom(
                                              fontSize: 13.sp,
                                              fontWeight: FontWeight.w400,
                                              color: RC.color707B7B),
                                        ),
                                        RichText(
                                            text: TextSpan(children: [
                                          TextSpan(
                                              // text: '${(double.parse('${rechargeController.moneyController.text}') - double.parse('${rechargeController.moneyController.text}') * (double.parse('${UserStore.to.userModel?.levelVipInfo?.fee ?? 0}') / 100)).toStringAsFixed(2)} ',
                                              text:
                                                  '${(double.parse('${rechargeController.getCoin}') * double.parse('$exchangeRate')).toStringAsFixed(5)} ',
                                              style: Styles.font_custom(
                                                  fontSize: 13.sp,
                                                  fontWeight: FontWeight.w500,
                                                  color: RC.black)),
                                          TextSpan(
                                              text: 'rare_treasure'.tr,
                                              style: Styles.font_custom(
                                                  fontSize: 13.sp,
                                                  fontWeight: FontWeight.w500,
                                                  color: RC.color707B7B))
                                        ]))
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 6.w),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'arrival_account'.tr,
                                          style: Styles.font_custom(
                                              fontSize: 13.sp,
                                              fontWeight: FontWeight.w400,
                                              color: RC.color707B7B),
                                        ),
                                        SizedBox(
                                          width: 200.w,
                                          child: Text(
                                            '${UserStore.to.userModel?.loginType == 'email' ? UserStore.to.userModel?.userEmail : UserStore.to.userModel?.mobile}',
                                            style: Styles.font_custom(
                                                fontSize: 13.sp,
                                                fontWeight: FontWeight.w500,
                                                color: RC.black),
                                            textAlign: TextAlign.end,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 6.w),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'purchase_channel'.tr,
                                          style: Styles.font_custom(
                                              fontSize: 13.sp,
                                              fontWeight: FontWeight.w400,
                                              color: RC.color707B7B),
                                        ),
                                        RichText(
                                            text: TextSpan(children: [
                                          WidgetSpan(
                                              child: Container(
                                            margin:
                                                EdgeInsets.only(right: 15.w),
                                            width: 14.w,
                                            height: 14.w,
                                            child: Image.asset(
                                                'assets/images/png/confirm_icon.png'),
                                          )),
                                          TextSpan(
                                              text: 'ATM_card'.tr,
                                              style: Styles.font_custom(
                                                  fontSize: 12.sp,
                                                  fontWeight: FontWeight.w400,
                                                  color: RC.color707B7B))
                                        ]))
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 12.w,
                                  ),
                                  Text(
                                    'if_you_have_any_questions'.tr,
                                    style: Styles.font_custom(
                                        fontSize: 11.sp,
                                        fontWeight: FontWeight.w400,
                                        color: RC.color707B7B),
                                  ),
                                  Text(
                                    '<EMAIL>',
                                    style: Styles.font_custom(
                                        fontSize: 11.sp,
                                        fontWeight: FontWeight.w400,
                                        color: RC.color707B7B),
                                  ),
                                  SizedBox(
                                    height: 10.w,
                                  ),
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      rechargeController.changeIsSelect();
                                    },
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 10.w,
                                          height: 10.w,
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  width: 1.w,
                                                  color: rechargeController
                                                          .isSelect
                                                      ? RC.color61D25F
                                                      : RC.color707B7B),
                                              borderRadius:
                                                  BorderRadius.circular(2.r),
                                              color: rechargeController.isSelect
                                                  ? RC.color61D25F
                                                  : Colors.transparent),
                                          child: Icon(
                                            Icons.check,
                                            size: 7.w,
                                            color: RC.white,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 4.w,
                                        ),
                                        Text(
                                          'i_have_read_and_agree_with_the_disclaimer'
                                              .tr,
                                          style: Styles.font_custom(
                                              fontSize: 11.sp,
                                              fontWeight: FontWeight.w400,
                                              color: RC.color707B7B),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    height: 22.w,
                                  ),
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      // if (rechargeController.isSelect == false) {
                                      //   CustomToast.showTextToast('please_read_and_agree_to_the_disclaimer'.tr);
                                      //   return ;
                                      // }
                                      // Get.back();
                                      // rechargeController.getThirdOrder();
                                      onConfirm?.call();
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                          color: RC.color61D25F,
                                          borderRadius:
                                              BorderRadius.circular(6.r)),
                                      width: double.infinity,
                                      padding:
                                          EdgeInsets.symmetric(vertical: 9.w),
                                      alignment: Alignment.center,
                                      child: Text(
                                        'recharge_now'.tr,
                                        style: Styles.font_custom(
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w500,
                                            color: RC.white),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 29.w,
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }));
        });
  }
}
